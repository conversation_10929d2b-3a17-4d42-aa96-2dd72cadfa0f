.nav-menu-3d {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-link {
  color: var(--thistle);
  font-weight: 600;
  text-decoration: none;
  padding: 0.5rem 1rem;
  position: relative;
  transition: transform 0.3s, color 0.3s;
  transform-style: preserve-3d;
}

.nav-link:hover {
  transform: translateZ(10px) rotateX(5deg);
  color: #fff;
}

.nav-link::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, transparent, var(--thistle), transparent);
  transform: scaleX(0);
  transition: transform 0.3s;
}

.nav-link:hover::before {
  transform: scaleX(1);
}

.nav-button {
  background: linear-gradient(135deg, var(--thistle), #9370DB);
  color: #fff;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-weight: 600;
  text-decoration: none;
  transition: transform 0.3s, box-shadow 0.3s;
  transform-style: preserve-3d;
}

.nav-button:hover {
  transform: translateZ(15px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

@media screen and (max-width: 767px) {
  .nav-menu-3d {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(20, 20, 30, 0.95);
    backdrop-filter: blur(10px);
    flex-direction: column;
    padding: 2rem;
    z-index: 100;
  }
  
  .nav-menu-3d.open {
    display: flex;
  }
}